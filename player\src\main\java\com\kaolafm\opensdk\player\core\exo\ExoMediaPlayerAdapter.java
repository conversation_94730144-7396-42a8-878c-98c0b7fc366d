package com.kaolafm.opensdk.player.core.exo;

import static androidx.media3.common.C.USAGE_MEDIA;

import android.content.Context;
import android.graphics.Bitmap;
import android.media.AudioDeviceInfo;
import android.media.session.PlaybackState;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.Surface;
import android.view.SurfaceHolder;

import androidx.annotation.OptIn;
import androidx.media3.common.AudioAttributes;
import androidx.media3.common.MediaItem;
import androidx.media3.common.MediaMetadata;
import androidx.media3.common.PlaybackException;
import androidx.media3.common.Player;
import androidx.media3.common.Timeline;
import androidx.media3.common.util.UnstableApi;
import androidx.media3.datasource.DefaultHttpDataSource;
import androidx.media3.exoplayer.DefaultLoadControl;
import androidx.media3.exoplayer.ExoPlayer;
import androidx.media3.exoplayer.source.DefaultMediaSourceFactory;
import androidx.media3.exoplayer.util.EventLogger;

import com.kaolafm.opensdk.player.core.ijk.VideoView;
import com.kaolafm.opensdk.player.core.listener.IPlayerBufferProgressListener;
import com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener;
import com.kaolafm.opensdk.player.core.listener.IPlayerStateCoreListener;
import com.kaolafm.opensdk.player.core.listener.IPlayerStateVideoCoreListener;
import com.kaolafm.opensdk.player.core.model.AMediaPlayer;
import com.kaolafm.opensdk.player.core.model.AudioFadeConfig;

import java.lang.ref.WeakReference;
import java.util.Collections;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

@OptIn(markerClass = UnstableApi.class)
public class ExoMediaPlayerAdapter extends AMediaPlayer {

    private final static String TAG = "ExoMediaPlayerAdapter";
    public final static String KEY_META_DURATION = "com.kaolafm.opensdk.player.core.exo.KEY_META_DURATION";

    private WeakReference<Context> mWrfContext;

    private volatile ExoPlayer mExoPlayer;

    private ExoYtListenerProxy listenerProxy;


    /**
     * 当前播放的媒体
     */
    private MediaItem curMediaItem;

    /**
     * 播放状态
     */
    private PlaybackState.Builder mPlaybackState;

    /**
     * 播放器元数据
     */
    private MediaMetadata.Builder mMediaMetadata;

    /**
     * 当前是否是 默认播放. 为了满足特殊需求.
     */
    private boolean isPlayNow = true;

    private final Player.Listener listener = new Player.Listener() {
        @Override
        public void onEvents(Player player, Player.Events events) {
        }

        @Override
        public void onTimelineChanged(Timeline timeline, int reason) {
        }

        @Override
        public void onMediaItemTransition(MediaItem mediaItem, int reason) {
            String mediaUri = null;
            if (mediaItem != null && mediaItem.localConfiguration != null) {
                mediaUri = mediaItem.localConfiguration.uri.toString();
            }
            Log.d(TAG, "onMediaItemTransition() called with: mediaItem.uri = [" + mediaUri + "], reason = [" + reason + "]");
            curMediaItem = mediaItem;
        }

        @Override
        public void onMediaMetadataChanged(MediaMetadata mediaMetadata) {
            mMediaMetadata.populate(mediaMetadata);
        }

        @Override
        public void onIsLoadingChanged(boolean isLoading) {
            Log.d(TAG, "onIsLoadingChanged() called with: isLoading = [" + isLoading + "]");
        }

        @Override
        public void onIsPlayingChanged(boolean isPlaying) {
            Log.d(TAG, "onIsPlayingChanged() called with: isPlaying = [" + isPlaying + "]");
            if (mExoPlayer.getPlaybackState() == Player.STATE_READY) {
                mPlaybackState.setState(isPlaying ? PlaybackState.STATE_PLAYING : PlaybackState.STATE_PAUSED
                        , mExoPlayer.getCurrentPosition(), mExoPlayer.getPlaybackParameters().speed);
            }
        }

        @Override
        public void onPlaybackStateChanged(int playbackState) {
            runUITask(() -> {
                long currentPosition = mExoPlayer.getCurrentPosition();
                float playbackSpeed = mExoPlayer.getPlaybackParameters().speed;
                boolean playWhenReady = mExoPlayer.getPlayWhenReady();
                Log.d(TAG, "onPlaybackStateChanged() called with: playbackState = [" + playbackState + "]" +
                        ", playWhenReady = [" + playWhenReady + "]" + ", currentPosition = [" + currentPosition + "], playbackSpeed = [" + playbackSpeed + "]");
                switch (playbackState) {
                    case Player.STATE_IDLE:
                        mPlaybackState.setState(PlaybackState.STATE_NONE, currentPosition, playbackSpeed);
                        break;
                    case Player.STATE_BUFFERING:
                        mPlaybackState.setState(PlaybackState.STATE_BUFFERING, currentPosition, playbackSpeed);
                        break;
                    case Player.STATE_READY:
//                        mPlaybackState.setState(mExoPlayer.getPlayWhenReady() ? PlaybackState.STATE_PLAYING : PlaybackState.STATE_PAUSED
//                                , currentPosition, playbackSpeed);
                        break;
                    case Player.STATE_ENDED:
                        mPlaybackState.setState(PlaybackState.STATE_STOPPED, currentPosition, playbackSpeed);
                        break;
                }
            });
        }

        @Override
        public void onPlayerError(PlaybackException error) {
            Log.d(TAG, "onPlayerError() called with: errorCode = [" + error.errorCode + "] ,errorMsg = [" + error.getMessage() + "]");
            runUITask(() -> {
                switch (error.errorCode) {
                    case PlaybackException.ERROR_CODE_BEHIND_LIVE_WINDOW:
                        // Re-initialize player at the live edge.
                        mExoPlayer.seekToDefaultPosition();
                        mExoPlayer.prepare();
                        return;
                    default:
                        // do nothing
                        break;
                }

                /*状态同步*/
                long currentPosition = mExoPlayer.getCurrentPosition();
                float playbackSpeed = mExoPlayer.getPlaybackParameters().speed;
                mPlaybackState.setState(PlaybackState.STATE_ERROR, currentPosition, playbackSpeed);
            });
        }
    };

    public ExoMediaPlayerAdapter(Context context) {
        super();
        mWrfContext = new WeakReference<>(context);
        initPLayer();
    }

    private synchronized void initPLayer() {
        if (mExoPlayer != null && !mExoPlayer.isReleased()) {
            return;
        }
        mPlaybackState = new PlaybackState.Builder();
        mMediaMetadata = new MediaMetadata.Builder();
        listenerProxy = new ExoYtListenerProxy(mPlaybackState, mMediaMetadata);

        runUITask(() -> {
            // 检查 Context 是否还有效
            Context context = mWrfContext.get();
            if (context == null) {
                Log.e(TAG, "initPLayer: Context has been garbage collected, cannot initialize ExoPlayer");
                return;
            }

            DefaultHttpDataSource.Factory httpDataSourceFactory = new DefaultHttpDataSource.Factory()
                    .setAllowCrossProtocolRedirects(true)
                    .setUserAgent("ExoPlayer")
                    .setDefaultRequestProperties(Collections.singletonMap("Connection", "keep-alive"));

            mExoPlayer = new ExoPlayer.Builder(context)
                    .setLoadControl(new DefaultLoadControl.Builder()
                            .setBufferDurationsMs(2500, DefaultLoadControl.DEFAULT_MAX_BUFFER_MS, 1500, 2000)
                            .build())
                    .setMediaSourceFactory(new DefaultMediaSourceFactory(new DefaultHttpDataSource.Factory()
                            .setAllowCrossProtocolRedirects(true)
                            .setDefaultRequestProperties(Collections.singletonMap("Connection", "keep-alive"))))
                    .build();
            mExoPlayer.setPlayWhenReady(false);

            // 音频焦点管理策略
            mExoPlayer.setAudioAttributes(new AudioAttributes.Builder().setUsage(USAGE_MEDIA).build()
                    , false);

            // 状态监听
            listenerProxy.setPlayer(mExoPlayer);
            mExoPlayer.addListener(listener);
            mExoPlayer.addAnalyticsListener(new EventLogger("ExoPlayerAnalytics"));
            mExoPlayer.addListener(listenerProxy.getPlayerListener());
            mExoPlayer.addAnalyticsListener(listenerProxy.getAnalyticsListener());

            Log.i(TAG, "initPLayer: mExoPlayer Initialization completed");
            postPlayerInitComplete();
        });
    }

    @Override
    public void initPlayerForce() {
        runUITask(() -> {
            if (mExoPlayer != null) {
                mExoPlayer.release();
                mExoPlayer = null;
            } else {
                initPLayer();
            }
        });
    }

    /**
     * 安全执行需要播放器的操作
     * @param action 需要执行的操作
     */
    private void runPlayerAction(Runnable action) {
        runUITask(() -> {
            if (mExoPlayer != null && !mExoPlayer.isReleased()) {
                action.run();
            } else {
                Log.w(TAG, "runPlayerAction: Player is not ready, action ignored");
            }
        });
    }

    @Override
    public void play() {
        Log.d(TAG, "play() called");
        runPlayerAction(() -> {
            // TODO: 2025/2/24 音频焦点判断逻辑
            mExoPlayer.play();
        });
    }

    @Override
    public void pause() {
        Log.d(TAG, "pause() called");
        runPlayerAction(() -> mExoPlayer.pause());
    }

    @Override
    public void stop() {
        Log.d(TAG, "stop() called");
        runUITask(() -> {
            mExoPlayer.stop();
        });
    }

    @Override
    public void rePlay() {
        Log.d(TAG, "rePlay() called");
        seekAtStart(0);
    }

    @Override
    public void start(String url, long duration, long position, int streamTypeChannel, boolean audioFadeEnabled, AudioFadeConfig audioFadeConfig, String httpProxy, boolean clearDnsCache, VideoView videoView, boolean needUseLastPlaybackRate, boolean shouldResetPlaybackRate) {
        Log.d(TAG, "start() called with: url = [" + url + "], duration = [" + duration + "], position = [" + position + "], streamTypeChannel = [" + streamTypeChannel + "], audioFadeEnabled = [" + audioFadeEnabled + "], audioFadeConfig = [" + audioFadeConfig + "], httpProxy = [" + httpProxy + "], clearDnsCache = [" + clearDnsCache + "], videoView = [" + videoView + "], needUseLastPlaybackRate = [" + needUseLastPlaybackRate + "], shouldResetPlaybackRate = [" + shouldResetPlaybackRate + "]");
        runUITask(() -> {
            Bundle extra = new Bundle();
            extra.putLong(KEY_META_DURATION, duration);
            mExoPlayer.setMediaItem(new MediaItem.Builder()
                            .setUri(url)
                            .setMediaMetadata(new MediaMetadata.Builder()
                                    .setDurationMs(duration)
                                    .setExtras(extra)
                                    .build()
                            )
                            .build()
                    , position);
            if (!needUseLastPlaybackRate) {
                resetPlaybackRate("start");
            }
            prepare();
            if (isPlayNow) {
                play();
            }
        });
    }

    @Override
    public void seek(long mSec) {
        Log.d(TAG, "seek() called with: mSec = [" + mSec + "]");
        runUITask(() -> {
            if (mExoPlayer.isCurrentMediaItemLive()) {
                Log.w(TAG, "current MediaItem not Seekable");
                return;
            }
            mExoPlayer.seekTo(mSec);
        });
    }

    @Override
    public boolean isPlaying() {
        return runUITaskSync(() -> mExoPlayer.isPlaying());
    }

    @Override
    public void setPlayerStateListener(IPlayerStateCoreListener listener) {
        listenerProxy.setStateListener(listener);
    }

    @Override
    public void setPlayerStateVideoListener(IPlayerStateVideoCoreListener listener) {
        listenerProxy.setStateVideoCoreListener(listener);
    }

    @Override
    public void setBufferProgressListener(IPlayerBufferProgressListener progressListener) {
        listenerProxy.setBufferProgressListener(progressListener);
    }

    /**
     * 设置播放器初始化成功回调
     *
     * @param initPlayerInitCompleteListener
     */
    @Override
    public void setInitPlayerInitCompleteListener(IPlayerInitCompleteListener initPlayerInitCompleteListener) {
        listenerProxy.setInitCompListener(initPlayerInitCompleteListener);
        postPlayerInitComplete();
    }

    /**
     * 设置进度
     *
     * @param urlDuration
     * @param totalDuration
     */
    @Override
    public void setDuration(long urlDuration, long totalDuration) {
        listenerProxy.setDuration(urlDuration, totalDuration);
    }

    @Override
    public void setPlayRatio(float ratio) {
        Log.d(TAG, "setPlayRatio() called with: ratio = [" + ratio + "]");
        runUITask(() -> {
            mExoPlayer.setPlaybackSpeed(ratio);
        });
    }

    @Override
    public void seekAtStart(long msec) {
        Log.d(TAG, "seekAtStart() called with: msec = [" + msec + "]");
        runUITask(() -> {
            if (!mExoPlayer.isCurrentMediaItemLive()) {
                Log.w(TAG, "current MediaItem not Seekable");
                return;
            }
            mExoPlayer.seekTo(msec);
        });
    }

    @Override
    public int getPlayStatus() {
        return ExoStateHelper.code2YtPlayState(mPlaybackState.build());
    }

    @Override
    public void setPlayStatus(int status) {
        // FIXME: 2025/2/25 为什么会需要外部设置播放状态？？？暂不理解
    }

    @Override
    public long getDuration() {
        return runUITaskSync(() -> mExoPlayer.getDuration());
    }

    @Override
    public long getCurrentPosition() {
        return runUITaskSync(() -> mExoPlayer.getCurrentPosition());
    }

    @Override
    public void preload(String url) {
        // do nothing
    }

    @Override
    public void reset(boolean needResetLastPlaybackRateFlag) {
        Log.d(TAG, "reset() called with: needResetLastPlaybackRateFlag = [" + needResetLastPlaybackRateFlag + "]");
        runUITask(() -> {
            mExoPlayer.stop();
            if (needResetLastPlaybackRateFlag) {
                resetPlaybackRate("reset");
            }
        });
    }

    @Override
    public void release() {
        Log.d(TAG, "release() called");
        runUITask(() -> {
            mExoPlayer.release();
            mExoPlayer.removeListener(listener);
            mExoPlayer.removeListener(listenerProxy.getPlayerListener());
        });
    }

    @Override
    public void prepare() {
        Log.d(TAG, "prepare() called");
        runUITask(() -> {
            if (mExoPlayer.getPlaybackState() == Player.STATE_IDLE) {
                mExoPlayer.prepare();
            }
        });
    }

    @Override
    public void prepareAsync() {
        Log.d(TAG, "prepareAsync() called");
        runUITask(() -> {
            mExoPlayer.prepare();
        });
    }

    @Override
    public void prepare(int needSeek) {
        Log.d(TAG, "prepare() called with: needSeek = [" + needSeek + "]");
        runUITask(() -> {
            mExoPlayer.prepare();
        });
    }

    @Override
    public void prepare(int needSeek, int stream_type_channel) {
        Log.d(TAG, "prepare() called with: needSeek = [" + needSeek + "], stream_type_channel = [" + stream_type_channel + "]");
        runUITask(() -> {
            mExoPlayer.prepare();
        });
    }

    @Override
    public void setDataSource(String source) {
        Log.d(TAG, "setDataSource() called with: source = [" + source + "]");

        // 检查 source 参数
        if (source == null || source.trim().isEmpty()) {
            Log.e(TAG, "setDataSource: source is null or empty");
            return;
        }

        try {
            MediaItem mediaItem = new MediaItem.Builder().setUri(source).build();
            runPlayerAction(() -> mExoPlayer.setMediaItem(mediaItem));
        } catch (Exception e) {
            Log.e(TAG, "setDataSource: Failed to create MediaItem or set media item", e);
        }
    }

    @Override
    public void setPlaybackRate(float rate) {
        runUITask(() -> mExoPlayer.setPlaybackSpeed(rate));
    }

    @Override
    public float getPlaybackRate() {
        if (mExoPlayer == null) {
            return DEF_PLAYBACKRATE;
        }
        return runUITaskSync(() -> mExoPlayer.getPlaybackParameters().speed);
    }

    @Override
    public void resetPlaybackRate(String refMethodName) {
        Log.d(TAG, "resetPlaybackRate() called with: refMethodName = [" + refMethodName + "]");
        runUITask(() -> {
            if (mExoPlayer != null) {
                mExoPlayer.setPlaybackSpeed(1F);
            }
        });
    }

    @Override
    public void setPreferredAudioDevice(AudioDeviceInfo audioDeviceInfo) {
        super.setPreferredAudioDevice(audioDeviceInfo);
        Log.d(TAG, "setPreferredAudioDevice() called with: audioDeviceInfo = [" + audioDeviceInfo + "]");
        runUITask(() -> {
            if (mExoPlayer != null) {
                mExoPlayer.setPreferredAudioDevice(audioDeviceInfo);
            }
        });
    }

    @Override
    public void setCustomAudioAttributes(android.media.AudioAttributes customAudioAttributes) {
        super.setCustomAudioAttributes(customAudioAttributes);
        if (customAudioAttributes == null) {
            Log.w(TAG, "setCustomAudioAttributes: customAudioAttributes can`t be NULL");
            return;
        }
        Log.d(TAG, "setCustomAudioAttributes() called with: Usage = [" + customAudioAttributes.getUsage() + "]" +
                ", ContentType = [" + customAudioAttributes.getContentType() + "]");

        runUITask(() -> {
            if (mExoPlayer == null) return;

            AudioAttributes customAttr = new AudioAttributes.Builder()
                    .setUsage(customAudioAttributes.getUsage()) // 例如切换到语音通话
                    .setContentType(customAudioAttributes.getContentType())
                    .build();

            mExoPlayer.setAudioAttributes(customAttr, false);
        });
    }

    @Override
    public void setDisplay(SurfaceHolder sh) {
        // TODO: 2025/2/25
    }

    @Override
    public void setSurface(Surface surface) {
        // TODO: 2025/2/25
    }

    @Override
    public boolean getCurrentFrame(Bitmap bitmap) {
        return false;
    }

    /**
     * 设置是否在prepare完成后自动播放
     * @param enabled boolean
     * true: 自动播放 默认值
     */
    @Override
    public void setPlayNowOnPrepared(boolean enabled) {
        isPlayNow = enabled;
        Log.d(TAG, "setPlayNowOnPrepared() called with: enabled = [" + enabled + "]");
    }

    @Override
    public boolean getPlayNow() {
        return isPlayNow;
    }

    /**
     * 播放器初始化完成回调
     */
    private void postPlayerInitComplete() {
        if (mExoPlayer != null) {
            listenerProxy.onPlayerInitComplete(true);
        }
    }

    //<editor-fold desc="线程调度">
    private Handler uiHandler = new Handler(Looper.getMainLooper());

    private void runUITask(Runnable runnable) {
        uiHandler.post(runnable);
    }

    private <T> T runUITaskSync(Callable<T> task) {
        // 检查是否在主线程
        if (Looper.myLooper() == Looper.getMainLooper()) {
            try {
                return task.call();
            } catch (Exception e) {
                Log.e(TAG, "runUITaskSync: ", e);
            }
        }

        final CountDownLatch latch = new CountDownLatch(1);
        final AtomicReference<T> result = new AtomicReference<>();
        final AtomicReference<Exception> exception = new AtomicReference<>();

        runUITask(() -> {
            try {
                result.set(task.call());
            } catch (Exception e) {
                exception.set(e);
                Log.e(TAG, "runUITaskSync: ", e);
            } finally {
                latch.countDown();
            }
        });

        try {
            // 添加超时时间，比如5秒
            if (!latch.await(5, TimeUnit.SECONDS)) {
                throw new RuntimeException("Task execution timeout");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Task interrupted", e);
        }

        if (exception.get() != null) {
            throw new RuntimeException(exception.get());
        }

        return result.get();
    }
    //</editor-fold>
}
